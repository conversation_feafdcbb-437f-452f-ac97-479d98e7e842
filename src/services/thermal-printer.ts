import { Transaction } from '@/interfaces/transaction';
import { formatThermalReceipt, ReceiptData } from '@/utils/thermal-receipt-formatter';

export interface PrinterConfig {
  printerName?: string;
  paperWidth?: number;
  encoding?: string;
  timeout?: number;
}

export interface PrintResult {
  success: boolean;
  message: string;
  error?: Error;
}

export class ThermalPrinterService {
  private config: PrinterConfig;
  private isSupported: boolean;

  constructor(config: PrinterConfig = {}) {
    this.config = {
      printerName: 'default',
      paperWidth: 58,
      encoding: 'utf-8',
      timeout: 10000,
      ...config,
    };

    this.isSupported = this.checkBrowserSupport();
  }

  private checkBrowserSupport(): boolean {
    return (
      typeof window !== 'undefined' &&
      'navigator' in window &&
      ('serial' in navigator || 'usb' in navigator || 'bluetooth' in navigator)
    );
  }

  async printReceipt(
    transaction: Transaction,
    businessInfo?: Partial<ReceiptData>,
  ): Promise<PrintResult> {
    try {
      if (!this.isSupported) {
        console.warn('Browser does not support printing APIs, using fallback method');
        return this.fallbackPrint(transaction, businessInfo);
      }

      const receiptData: ReceiptData = {
        transaction,
        businessName: businessInfo?.businessName || 'Propaga',
        businessAddress: businessInfo?.businessAddress,
        businessPhone: businessInfo?.businessPhone,
      };

      const receiptContent = formatThermalReceipt(receiptData);

      const printMethods = [
        () => this.printViaWebSerial(receiptContent),
        () => this.printViaWebUSB(receiptContent),
        () => this.printViaBluetooth(receiptContent),
        () => this.printViaChromePrinterAPI(receiptContent),
        () => this.fallbackPrint(transaction, businessInfo),
      ];

      for (const printMethod of printMethods) {
        try {
          const result = await printMethod();
          if (result.success) {
            return result;
          }
        } catch (error) {
          console.warn('Print method failed, trying next method:', error);
          continue;
        }
      }

      return {
        success: false,
        message: 'No se pudo conectar con ninguna impresora térmica',
        error: new Error('All print methods failed'),
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error al imprimir el recibo',
        error: error as Error,
      };
    }
  }

  private async printViaChromePrinterAPI(content: string): Promise<PrintResult> {
    if (!('showPrintDialog' in window)) {
      throw new Error('Chrome Print API not supported');
    }
    const printWindow = window.open('', '_blank', 'width=400,height=600');
    if (!printWindow) {
      throw new Error('Could not open print window');
    }
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Recibo de Transacción</title>
        <style>
          body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            margin: 0;
            padding: 10px;
            white-space: pre-wrap;
          }
          @media print {
            body { margin: 0; padding: 5px; }
          }
        </style>
      </head>
      <body>${content}</body>
      </html>
    `);

    printWindow.document.close();
    printWindow.focus();

    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 500);

    return {
      success: true,
      message: 'Recibo impreso exitosamente via Chrome Print API',
    };
  }

  private async printViaWebSerial(content: string): Promise<PrintResult> {
    if (!('serial' in navigator)) {
      throw new Error('Web Serial API not supported');
    }

    try {
      // Request a port
      const port = await (navigator as any).serial.requestPort();

      // Open the port
      await port.open({
        baudRate: 9600,
        dataBits: 8,
        stopBits: 1,
        parity: 'none',
      });

      // Get a writer
      const writer = port.writable.getWriter();

      // Convert content to bytes and send
      const encoder = new TextEncoder();
      const data = encoder.encode(content);

      await writer.write(data);

      // Send cut command (ESC/POS)
      const cutCommand = new Uint8Array([0x1d, 0x56, 0x00]); // GS V 0
      await writer.write(cutCommand);

      // Release the writer and close the port
      writer.releaseLock();
      await port.close();

      return {
        success: true,
        message: 'Recibo impreso exitosamente via Serial',
      };
    } catch (error) {
      throw new Error(`Serial printing failed: ${error}`);
    }
  }

  /**
   * Print via Web USB API
   */
  private async printViaWebUSB(content: string): Promise<PrintResult> {
    if (!('usb' in navigator)) {
      throw new Error('Web USB API not supported');
    }

    try {
      // Request a USB device (thermal printer)
      const device = await (navigator as any).usb.requestDevice({
        filters: [
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x0519 }, // Star Micronics
          { vendorId: 0x154f }, // Citizen
        ],
      });

      await device.open();
      await device.selectConfiguration(1);
      await device.claimInterface(0);

      // Convert content to bytes
      const encoder = new TextEncoder();
      const data = encoder.encode(content);

      // Send data to printer
      await device.transferOut(1, data);

      // Send cut command
      const cutCommand = new Uint8Array([0x1d, 0x56, 0x00]);
      await device.transferOut(1, cutCommand);

      await device.close();

      return {
        success: true,
        message: 'Recibo impreso exitosamente via USB',
      };
    } catch (error) {
      throw new Error(`USB printing failed: ${error}`);
    }
  }

  /**
   * Print via Web Bluetooth API
   */
  private async printViaBluetooth(content: string): Promise<PrintResult> {
    if (!('bluetooth' in navigator)) {
      throw new Error('Web Bluetooth API not supported');
    }

    try {
      // Request a Bluetooth device
      const device = await (navigator as any).bluetooth.requestDevice({
        acceptAllDevices: true,
        optionalServices: ['000018f0-0000-1000-8000-00805f9b34fb'], // Generic service
      });

      const server = await device.gatt.connect();
      const service = await server.getPrimaryService('000018f0-0000-1000-8000-00805f9b34fb');
      const characteristic = await service.getCharacteristic(
        '00002af1-0000-1000-8000-00805f9b34fb',
      );

      // Convert content to bytes and send
      const encoder = new TextEncoder();
      const data = encoder.encode(content);

      await characteristic.writeValue(data);

      await device.gatt.disconnect();

      return {
        success: true,
        message: 'Recibo impreso exitosamente via Bluetooth',
      };
    } catch (error) {
      throw new Error(`Bluetooth printing failed: ${error}`);
    }
  }

  /**
   * Fallback printing method using browser's print dialog
   */
  private async fallbackPrint(
    transaction: Transaction,
    businessInfo?: Partial<ReceiptData>,
  ): Promise<PrintResult> {
    try {
      const receiptData: ReceiptData = {
        transaction,
        businessName: businessInfo?.businessName || 'Propaga',
        businessAddress: businessInfo?.businessAddress,
        businessPhone: businessInfo?.businessPhone,
      };

      const receiptContent = formatThermalReceipt(receiptData);

      // Create a new window for printing
      const printWindow = window.open('', '_blank', 'width=400,height=600');

      if (!printWindow) {
        throw new Error('Could not open print window');
      }

      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Recibo de Transacción</title>
          <style>
            body {
              font-family: 'Courier New', monospace;
              font-size: 12px;
              line-height: 1.2;
              margin: 0;
              padding: 10px;
              white-space: pre-wrap;
            }
            @media print {
              body { margin: 0; padding: 5px; }
            }
          </style>
        </head>
        <body>${receiptContent}</body>
        </html>
      `);

      printWindow.document.close();
      printWindow.focus();

      // Wait a bit for content to load, then print
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
      }, 500);

      return {
        success: true,
        message: 'Recibo enviado a impresora del sistema',
      };
    } catch (error) {
      return {
        success: false,
        message: 'Error al abrir ventana de impresión',
        error: error as Error,
      };
    }
  }

  /**
   * Test printer connectivity
   */
  async testPrinter(): Promise<PrintResult> {
    const testContent = `
PRUEBA DE IMPRESORA
==================
Fecha: ${new Date().toLocaleString()}
Estado: Conectada
==================
    `;

    try {
      if (this.isSupported) {
        return await this.printViaWebSerial(testContent);
      } else {
        return await this.fallbackPrint({} as Transaction);
      }
    } catch (error) {
      return {
        success: false,
        message: 'Error en prueba de impresora',
        error: error as Error,
      };
    }
  }
}

// Export a default instance
export const thermalPrinter = new ThermalPrinterService();
