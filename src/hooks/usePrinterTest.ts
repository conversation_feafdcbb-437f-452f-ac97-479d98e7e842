import { useState, useCallback } from 'react';
import { useToast } from '@chakra-ui/react';

interface TestResult {
  message: string;
  type: 'info' | 'success' | 'error';
  timestamp: string;
}

interface DeviceInfo {
  vendorId: string;
  productId: string;
  productName: string;
  manufacturerName: string;
  serialNumber: string;
}

export const usePrinterTest = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const toast = useToast();

  const addResult = useCallback((message: string, type: 'info' | 'success' | 'error' = 'info') => {
    const result: TestResult = {
      message,
      type,
      timestamp: new Date().toLocaleTimeString(),
    };
    setTestResults((prev) => [...prev, result]);
  }, []);

  const clearResults = useCallback(() => {
    setTestResults([]);
    setDeviceInfo(null);
  }, []);

  const displayDeviceInfo = useCallback((device: any) => {
    const info: DeviceInfo = {
      vendorId: `0x${device.vendorId.toString(16).padStart(4, '0')}`,
      productId: `0x${device.productId.toString(16).padStart(4, '0')}`,
      productName: device.productName || 'Unknown',
      manufacturerName: device.manufacturerName || 'Unknown',
      serialNumber: device.serialNumber || 'Unknown',
    };
    setDeviceInfo(info);
  }, []);

  const checkWebUSBSupport = useCallback(() => {
    return typeof window !== 'undefined' && 'usb' in navigator;
  }, []);

  const testWebUSB = useCallback(async () => {
    setIsLoading(true);
    addResult('Testing Web USB API support...');

    if (checkWebUSBSupport()) {
      addResult('✅ Web USB API is supported', 'success');
      toast({
        title: 'Web USB Supported',
        description: 'Your browser supports Web USB API',
        status: 'success',
        duration: 3000,
      });
    } else {
      addResult('❌ Web USB API is not supported', 'error');
      toast({
        title: 'Web USB Not Supported',
        description: 'Please use Chrome, Edge, or Opera browser',
        status: 'error',
        duration: 5000,
      });
    }
    setIsLoading(false);
  }, [addResult, checkWebUSBSupport, toast]);

  const requestDevice = useCallback(async () => {
    try {
      if (!checkWebUSBSupport()) {
        throw new Error('Web USB not supported');
      }

      const device = await (navigator as any).usb.requestDevice({
        filters: [
          { vendorId: 0x04b8 }, // Epson
          { vendorId: 0x0519 }, // Star Micronics
          { vendorId: 0x154f }, // Citizen
          { vendorId: 0x0483 }, // STMicroelectronics (some Epson models)
          { vendorId: 0x20d1 }, // Rongta (common thermal printer brand)
        ],
      });

      addResult('✅ Device selected successfully', 'success');
      displayDeviceInfo(device);
      return device;
    } catch (error: any) {
      if (error.name === 'NotFoundError') {
        addResult('❌ No device selected or no compatible devices found', 'error');
      } else {
        addResult(`❌ Device selection failed: ${error.message}`, 'error');
      }
      return null;
    }
  }, [addResult, checkWebUSBSupport, displayDeviceInfo]);

  const detectDevices = useCallback(async () => {
    setIsLoading(true);
    addResult('Detecting USB devices...');

    try {
      if (!checkWebUSBSupport()) {
        throw new Error('Web USB not supported');
      }

      const devices = await (navigator as any).usb.getDevices();
      const epsonDevices = devices.filter((device: any) => device.vendorId === 0x04b8);

      if (epsonDevices.length > 0) {
        addResult(`✅ Found ${epsonDevices.length} Epson device(s)`, 'success');
        displayDeviceInfo(epsonDevices[0]);
        toast({
          title: 'Devices Found',
          description: `Found ${epsonDevices.length} Epson device(s)`,
          status: 'success',
          duration: 3000,
        });
      } else {
        addResult('⚠️ No Epson devices found. Requesting device access...', 'info');
        await requestDevice();
      }
    } catch (error: any) {
      addResult(`❌ Error detecting devices: ${error.message}`, 'error');
      toast({
        title: 'Detection Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
    }
    setIsLoading(false);
  }, [addResult, checkWebUSBSupport, displayDeviceInfo, requestDevice, toast]);

  const testConnection = useCallback(async () => {
    setIsLoading(true);
    addResult('Testing printer connection...');

    try {
      const device = await requestDevice();
      if (!device) return false;

      await device.open();
      addResult('✅ Device opened successfully', 'success');

      if (device.configurations.length === 0) {
        throw new Error('No configurations available');
      }

      await device.selectConfiguration(1);
      addResult('✅ Configuration selected', 'success');

      const config = device.configuration;
      if (!config?.interfaces || config.interfaces.length === 0) {
        throw new Error('No interfaces available');
      }

      await device.claimInterface(0);
      addResult('✅ Interface claimed', 'success');

      // Find OUT endpoint
      const interface0 = config.interfaces[0];
      const alternate = interface0.alternates[0];
      const outEndpoint = alternate.endpoints.find((ep: any) => ep.direction === 'out');

      if (!outEndpoint) {
        throw new Error('No OUT endpoint found');
      }

      addResult(`✅ Found OUT endpoint: ${outEndpoint.endpointNumber}`, 'success');

      await device.close();
      addResult('✅ Connection test completed successfully', 'success');

      toast({
        title: 'Connection Successful',
        description: 'Printer connection test passed',
        status: 'success',
        duration: 3000,
      });

      return true;
    } catch (error: any) {
      addResult(`❌ Connection test failed: ${error.message}`, 'error');
      toast({
        title: 'Connection Failed',
        description: error.message,
        status: 'error',
        duration: 5000,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [addResult, requestDevice, toast]);

  const printTestReceipt = useCallback(
    async (receiptContent: string) => {
      setIsLoading(true);
      addResult('Printing test receipt...');

      try {
        const device = await requestDevice();
        if (!device) return false;

        await device.open();
        await device.selectConfiguration(1);
        await device.claimInterface(0);

        // Get the interface and find the correct endpoint
        const config = device.configuration;
        const interface0 = config.interfaces[0];
        const alternate = interface0.alternates[0];
        const outEndpoint = alternate.endpoints.find((ep: any) => ep.direction === 'out');

        if (!outEndpoint) {
          throw new Error('No OUT endpoint found');
        }

        // Initialize printer with ESC/POS commands
        const initCommands = new Uint8Array([
          0x1b,
          0x40, // ESC @ - Initialize printer
          0x1b,
          0x61,
          0x00, // ESC a 0 - Left align
          0x1b,
          0x21,
          0x00, // ESC ! 0 - Normal text
        ]);

        await device.transferOut(outEndpoint.endpointNumber, initCommands);
        addResult('✅ Printer initialized', 'success');

        // Convert content to bytes
        const encoder = new TextEncoder();
        const data = encoder.encode(receiptContent);

        // Send data to printer in chunks
        const chunkSize = 64;
        for (let i = 0; i < data.length; i += chunkSize) {
          const chunk = data.slice(i, i + chunkSize);
          await device.transferOut(outEndpoint.endpointNumber, chunk);
          await new Promise((resolve) => setTimeout(resolve, 10));
        }

        addResult('✅ Receipt data sent', 'success');

        // Send cut command (ESC/POS standard)
        const cutCommand = new Uint8Array([
          0x1d,
          0x56,
          0x00, // GS V 0 - Full cut
          0x0a,
          0x0a,
          0x0a, // Line feeds for paper advance
        ]);
        await device.transferOut(outEndpoint.endpointNumber, cutCommand);

        await device.close();
        addResult('✅ Test receipt printed successfully', 'success');

        toast({
          title: 'Print Successful',
          description: 'Test receipt printed successfully',
          status: 'success',
          duration: 3000,
        });

        return true;
      } catch (error: any) {
        addResult(`❌ Print test failed: ${error.message}`, 'error');
        toast({
          title: 'Print Failed',
          description: error.message,
          status: 'error',
          duration: 5000,
        });
        return false;
      } finally {
        setIsLoading(false);
      }
    },
    [addResult, requestDevice, toast],
  );

  return {
    testResults,
    deviceInfo,
    isLoading,
    addResult,
    clearResults,
    checkWebUSBSupport,
    testWebUSB,
    detectDevices,
    testConnection,
    printTestReceipt,
  };
};
